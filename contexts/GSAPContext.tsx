'use client';

import { ReactNode, createContext, useContext } from 'react';
import { gsap } from 'gsap';
import { useGSAP } from '@gsap/react';

const GSAPContext = createContext<typeof gsap | null>(null);

export const GSAPProvider = ({ children }: { children: ReactNode }) => {
  useGSAP(() => {
    // GSAP plugins can be registered here if needed
  });

  return (
    <GSAPContext.Provider value={gsap}>
      {children}
    </GSAPContext.Provider>
  );
};

export const useGSAPContext = () => {
  const context = useContext(GSAPContext);
  if (!context) {
    throw new Error('useGSAPContext must be used within a GSAPProvider');
  }
  return context;
};
