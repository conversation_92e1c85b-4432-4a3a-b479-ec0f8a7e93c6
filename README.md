# NACIT Lilongwe - AI Assistant

This project features a modern AI assistant component built with Next.js, React, Tailwind CSS, and GSAP.

## Features

- 🚀 Floating chat button with smooth animations
- 💬 Interactive chat interface
- ✨ GSAP-powered animations for a polished feel
- 📱 Fully responsive design
- 💅 Modern UI with Tailwind CSS
- ⚡ Optimized for performance

## Prerequisites

- Node.js 18.0.0 or later
- npm or pnpm (recommended)

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nacit-ai-html
   ```

2. **Install dependencies**
   ```bash
   # Using npm
   npm install
   
   # Or using pnpm (recommended)
   pnpm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Visit [http://localhost:3000](http://localhost:3000) to see the application in action.

## Project Structure

- `/app` - Next.js app directory
  - `layout.tsx` - Root layout with GSAP provider
  - `page.tsx` - Home page
  - `globals.css` - Global styles
- `/components` - Reusable components
  - `AIAssistant.tsx` - Main AI Assistant component
- `/contexts` - React contexts
  - `GSAPContext.tsx` - GSAP context provider
- `/public` - Static files
- `tailwind.config.js` - Tailwind CSS configuration
- `postcss.config.js` - PostCSS configuration
- `package.json` - Project dependencies and scripts

## Customization

### Colors

You can customize the color scheme by modifying the `tailwind.config.js` file. The primary color palette can be found under the `theme.extend.colors.primary` section.

### Animations

The AI assistant includes several GSAP animations. You can adjust these in the `AIAssistant.tsx` component.

## Deployment

### Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-docs) from the creators of Next.js.

### Docker

A `Dockerfile` is included for containerized deployments:

```bash
docker build -t nacit-ai-assistant .
docker run -p 3000:3000 nacit-ai-assistant
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
