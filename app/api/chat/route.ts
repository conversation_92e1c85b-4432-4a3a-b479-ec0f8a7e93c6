import { NextResponse } from 'next/server';
import getConfig from 'next/config';

interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
}

interface ToolDefinition {
  name: string;
  description: string;
  parameters: Record<string, any>;
}

interface Message {
  role: 'user' | 'assistant' | 'tool';
  content: string;
  name?: string;
  tool_calls?: Array<{
    id: string;
    type: 'function';
    function: {
      name: string;
      arguments: string;
    };
  }>;
  tool_call_id?: string;
}

interface RequestBody {
  query: string;
  messages?: Message[];
  tools?: ToolDefinition[];
}

interface LocalAPIResponse {
  response?: string;
  error?: string;
  details?: unknown;
}

async function callMcpTool(toolCall: ToolCall) {
  try {
    const response = await fetch('http://localhost:8787/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tool: toolCall.name,
        parameters: toolCall.arguments,
      }),
    });

    if (!response.ok) {
      throw new Error(`Tool call failed: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error calling MCP tool:', error);
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    // Get server runtime config
    const { serverRuntimeConfig } = getConfig();
    const { 
      cloudflareAccountId,
      cloudflareApiToken,
      cloudflareRagId 
    } = serverRuntimeConfig;

    console.log('Cloudflare Configuration:', {
      accountId: cloudflareAccountId ? '***' + cloudflareAccountId.slice(-4) : 'MISSING',
      ragId: cloudflareRagId ? '***' + cloudflareRagId.slice(-4) : 'MISSING',
      apiToken: cloudflareApiToken ? '***' + cloudflareApiToken.slice(-4) : 'MISSING',
      env: process.env.NODE_ENV,
      hasAccountId: !!cloudflareAccountId,
      hasRagId: !!cloudflareRagId,
      hasApiToken: !!cloudflareApiToken
    });
    
    const { query, messages = [], tools } = (await request.json()) as RequestBody;
    console.log('Received query:', query);

    if (!query) {
      return NextResponse.json(
        { error: 'Query is required' },
        { status: 400 }
      );
    }

    // Add user message to conversation history
    const conversation = [
      ...messages,
      { role: 'user' as const, content: query }
    ];

    // Make initial request to AutoRAG
    const apiUrl = `https://api.cloudflare.com/client/v4/accounts/${cloudflareAccountId}/autorag/rags/${cloudflareRagId}/ai-search`;
    
    // First, send the query to get the initial response
    const initialResponse = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${cloudflareApiToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: query,
        stream: false,
      }),
    });

    if (!initialResponse.ok) {
      const errorText = await initialResponse.text();
      console.error('Cloudflare API error:', initialResponse.status, errorText);
      return NextResponse.json(
        { error: `Cloudflare API Error: ${errorText}` },
        { status: initialResponse.status }
      );
    }

    const initialResult = await initialResponse.json();
    console.log('Initial Cloudflare API response:', JSON.stringify(initialResult, null, 2));
    
    // Check if we have a direct response from AutoRAG
    if (initialResult?.success && initialResult.result?.response) {
      console.log('Returning direct response from AutoRAG');
      return new Response(JSON.stringify({ 
        content: initialResult.result.response,
        isToolCall: false
      }), {
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }
    
    // Check for chat completion format (if different from AutoRAG response)
    if (initialResult.choices?.[0]?.message) {
      const assistantMessage = initialResult.choices[0].message;
      console.log('Assistant message:', JSON.stringify(assistantMessage, null, 2));
      
      // If no tool calls, return the assistant's response
      if (!assistantMessage?.tool_calls?.length) {
        console.log('No tool calls detected, returning direct response');
        return new Response(JSON.stringify({ 
          content: assistantMessage?.content || 'No response',
          isToolCall: false
        }), {
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }
    }
    
    // If we get here, the response format is unexpected
    console.error('Unexpected response format from Cloudflare API:', initialResult);
    return NextResponse.json(
      { error: 'Unexpected response format from Cloudflare API' },
      { status: 500 }
    );
  } catch (error) {
    console.error('Error in chat API route:', error);
    return NextResponse.json(
      { 
        error: 'An error occurred while processing your request',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined
      } as LocalAPIResponse,
      { status: 500 }
    );
  }
}
