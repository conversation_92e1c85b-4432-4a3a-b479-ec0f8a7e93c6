import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { GSAPProvider } from '../contexts/GSAPContext';
import './globals.css';
import { IconoirProvider } from 'iconoir-react';
import AIAssistant from '../components/AIAssistant';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'NACIT | Lilongwe',
  description: 'National College of Information Technology - Lilongwe',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <IconoirProvider
          iconProps={{
            color: '#AAAAAA',
            strokeWidth: 1,
            width: '1em',
            height: '1em',
          }}
        >
          <GSAPProvider>
            {children}
            <AIAssistant />
          </GSAPProvider>
        </IconoirProvider>
      </body>
    </html>
  );
}
