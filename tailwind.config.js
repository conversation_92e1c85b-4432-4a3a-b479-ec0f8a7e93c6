/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './contexts/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          950: '#082f49',
        },
      },
      animation: {
        'bounce-slow': 'bounce 2s infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'gradient': 'gradient 4s ease infinite',
      },
      keyframes: {
        gradient: {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
      },
      backgroundSize: {
        'size-200': '200% 200%',
      },
      backgroundPosition: {
        'pos-0': '0% 0%',
        'pos-100': '100% 100%',
      },
      typography: (theme) => ({
        DEFAULT: {
          css: {
            color: theme('colors.gray.800'),
            maxWidth: 'none',
            '--tw-prose-body': theme('colors.gray.800'),
            '--tw-prose-headings': theme('colors.gray.900'),
            '--tw-prose-links': theme('colors.blue.600'),
            '--tw-prose-links-hover': theme('colors.blue.800'),
            '--tw-prose-underline': theme('colors.blue.600'),
            '--tw-prose-bold': theme('colors.gray.900'),
            '--tw-prose-counters': theme('colors.gray.500'),
            '--tw-prose-bullets': theme('colors.gray.500'),
            '--tw-prose-hr': theme('colors.gray.200'),
            '--tw-prose-quote': theme('colors.gray.700'),
            '--tw-prose-quote-borders': theme('colors.gray.300'),
            '--tw-prose-captions': theme('colors.gray.500'),
            '--tw-prose-code': theme('colors.gray.900'),
            '--tw-prose-pre-code': theme('colors.gray.200'),
            '--tw-prose-pre-bg': theme('colors.gray.900'),
            '--tw-prose-th-borders': theme('colors.gray.300'),
            '--tw-prose-td-borders': theme('colors.gray.200'),
            '--tw-prose-invert-body': theme('colors.gray.200'),
            '--tw-prose-invert-headings': theme('colors.white'),
            '--tw-prose-invert-links': theme('colors.blue.400'),
            '--tw-prose-invert-links-hover': theme('colors.blue.300'),
            '--tw-prose-invert-underline': theme('colors.blue.400'),
            '--tw-prose-invert-bold': theme('colors.white'),
            '--tw-prose-invert-counters': theme('colors.gray.400'),
            '--tw-prose-invert-bullets': theme('colors.gray.600'),
            '--tw-prose-invert-hr': theme('colors.gray.700'),
            '--tw-prose-invert-quote': theme('colors.gray.300'),
            '--tw-prose-invert-quote-borders': theme('colors.gray.600'),
            '--tw-prose-invert-captions': theme('colors.gray.400'),
            '--tw-prose-invert-code': theme('colors.white'),
            '--tw-prose-invert-pre-code': theme('colors.gray.300'),
            '--tw-prose-invert-pre-bg': theme('colors.gray.800'),
            '--tw-prose-invert-th-borders': theme('colors.gray.600'),
            '--tw-prose-invert-td-borders': theme('colors.gray.700'),
            'h1, h2, h3, h4, h5, h6': {
              marginTop: '1.5em',
              marginBottom: '0.5em',
              fontWeight: '600',
              lineHeight: 1.3,
            },
            'p, ul, ol, blockquote, pre, table': {
              marginTop: '1em',
              marginBottom: '1em',
            },
            'ul, ol': {
              paddingLeft: '1.5em',
            },
            'ul > li': {
              paddingLeft: '0.375em',
              marginTop: '0.25em',
              marginBottom: '0.25em',
            },
            'ol > li': {
              paddingLeft: '0.375em',
              marginTop: '0.25em',
              marginBottom: '0.25em',
            },
            '> ul > li > :first-child': {
              marginTop: '0.75em',
            },
            '> ul > li > :last-child': {
              marginBottom: '0.75em',
            },
            '> ol > li > :first-child': {
              marginTop: '0.75em',
            },
            '> ol > li > :last-child': {
              marginBottom: '0.75em',
            },
            'ul ul, ul ol, ol ul, ol ol': {
              marginTop: '0.5em',
              marginBottom: '0.5em',
            },
            'h2': {
              fontSize: '1.5em',
              marginTop: '1.6em',
              marginBottom: '0.8em',
              lineHeight: 1.3,
            },
            'h3': {
              fontSize: '1.25em',
              marginTop: '1.4em',
              marginBottom: '0.7em',
              lineHeight: 1.4,
            },
            'h4': {
              fontSize: '1.1em',
              marginTop: '1.2em',
              marginBottom: '0.6em',
              lineHeight: 1.4,
            },
            'a': {
              textDecoration: 'none',
              fontWeight: '500',
              '&:hover': {
                textDecoration: 'underline',
              },
            },
            'code': {
              backgroundColor: theme('colors.gray.100'),
              color: theme('colors.gray.900'),
              padding: '0.2em 0.4em',
              borderRadius: '0.25rem',
              fontSize: '0.9em',
              fontWeight: 'normal',
              '&::before, &::after': {
                content: 'none',
              },
            },
            'pre': {
              backgroundColor: theme('colors.gray.900'),
              color: theme('colors.gray.100'),
              padding: '1rem',
              borderRadius: '0.5rem',
              overflowX: 'auto',
              marginTop: '1.25em',
              marginBottom: '1.25em',
              fontSize: '0.9em',
              lineHeight: 1.5,
            },
            'pre code': {
              backgroundColor: 'transparent',
              color: 'inherit',
              padding: 0,
              borderRadius: 0,
              fontSize: 'inherit',
            },
            'blockquote': {
              borderLeftWidth: '4px',
              borderLeftColor: theme('colors.gray.300'),
              paddingLeft: '1rem',
              fontStyle: 'italic',
              color: theme('colors.gray.700'),
              '> :first-child': {
                marginTop: 0,
              },
              '> :last-child': {
                marginBottom: 0,
              },
            },
            'table': {
              width: '100%',
              borderCollapse: 'collapse',
              marginTop: '1.5em',
              marginBottom: '1.5em',
              fontSize: '0.9em',
              lineHeight: 1.5,
            },
            'th': {
              backgroundColor: theme('colors.gray.100'),
              fontWeight: '600',
              textAlign: 'left',
              padding: '0.5rem 0.75rem',
              border: `1px solid ${theme('colors.gray.200')}`,
            },
            'td': {
              padding: '0.5rem 0.75rem',
              border: `1px solid ${theme('colors.gray.200')}`,
            },
            'tr:nth-child(even)': {
              backgroundColor: theme('colors.gray.50'),
            },
            'img': {
              maxWidth: '100%',
              height: 'auto',
              borderRadius: '0.375rem',
              marginTop: '1.5em',
              marginBottom: '1.5em',
            },
          },
        },
      }),
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
  variants: {
    typography: ['dark'],
  },
}
