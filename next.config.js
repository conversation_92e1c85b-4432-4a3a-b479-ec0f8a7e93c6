/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Only expose non-sensitive environment variables to the client
  env: {
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  },
  // Ensure server-side environment variables are loaded correctly
  serverRuntimeConfig: {
    // For server-side only
    cloudflareAccountId: process.env.CLOUDFLARE_ACCOUNT_ID,
    cloudflareApiToken: process.env.CLOUDFLARE_API_TOKEN,
    cloudflareRagId: process.env.CLOUDFLARE_RAG_ID,
  },
  // Add webpack configuration if needed
  webpack: (config, { isServer }) => {
    // Add any webpack config here
    return config;
  },
};

module.exports = nextConfig;
