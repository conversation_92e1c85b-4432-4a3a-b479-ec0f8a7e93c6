'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { gsap } from 'gsap';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';
import { useGSAP } from '@gsap/react';
import { ChatBubble, SendDiagonal, Sparks, SystemRestart } from 'iconoir-react';
import { MessageItem } from './MessageItem';
import { useMcp } from 'use-mcp/react'


gsap.registerPlugin(ScrollToPlugin);

type Message = {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isError?: boolean;
  isTyping?: boolean;
};

export default function AIAssistant() {

  const {
    state,          // Connection state: 'discovering' | 'authenticating' | 'connecting' | 'loading' | 'ready' | 'failed'
    tools,          // Available tools from MCP server
    error,          // Error message if connection failed
    callTool,       // Function to call tools on the MCP server
    retry,          // Retry connection manually
    authenticate,   // Manually trigger authentication
    clearStorage,   // Clear stored tokens and credentials
  } = useMcp({
    url: process.env.NEXT_PUBLIC_MCP_SERVER_URL || 'http://localhost:8787/sse',
    clientName: 'NACIT AI Assistant',
    autoReconnect: true,
    debug: true,
  });

  useEffect(() => {
    console.log('MCP State:', state);
    console.log('MCP Error:', error);
    console.log('Available Tools:', tools);
  }, [state, error, tools]);

  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  // const [isMinimized, setIsMinimized] = useState(false);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const chatIconRef = useRef<HTMLDivElement>(null);
  const closeIconRef = useRef<HTMLDivElement>(null);
  const timeline = useRef<gsap.core.Timeline | null>(null);
  const userHasScrolled = useRef<boolean>(false);
  const lastScrollPosition = useRef<number>(0);
  const scrollTween = useRef<gsap.core.Tween | null>(null);

  // Focus input when chat is opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Initialize GSAP context
  useGSAP(() => {
    // Initial animation for the button
    gsap.from(buttonRef.current, {
      scale: 0,
      opacity: 0,
      duration: 0.5,
      ease: 'back.out(1.7)',
      delay: 1,
    });

    // Set initial state for chat container
    gsap.set(chatContainerRef.current, {
      scale: 0.8,
      opacity: 0,
      y: 20,
      transformOrigin: 'bottom right',
      visibility: 'hidden',
    });
  }, []);

  useEffect(() => {
    gsap.registerPlugin(ScrollToPlugin);

    if (buttonRef.current && chatIconRef.current && closeIconRef.current) {
      // Set initial states
      gsap.set(closeIconRef.current, { opacity: 0, scale: 0, rotation: 90 });
      gsap.set(chatIconRef.current, { opacity: 1, scale: 1, rotation: 0 });

      timeline.current = gsap.timeline({ paused: true })
        .to(buttonRef.current, {
          rotation: '+=180',
          scale: 1.1,
          duration: 0.4,
          ease: 'power2.inOut'
        })
        .to(chatIconRef.current, {
          opacity: 0,
          scale: 0.5,
          rotation: -90,
          duration: 0.4,
          ease: 'power2.inOut'
        }, 0)
        .to(closeIconRef.current, {
          opacity: 1,
          scale: 1,
          rotation: 0,
          duration: 0.4,
          ease: 'power2.inOut'
        }, "<0.1");
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      gsap.to(chatContainerRef.current, {
        visibility: 'visible',
        scale: 1,
        opacity: 1,
        y: 0,
        duration: 0.3,
        ease: 'power2.out',
      });
    } else {
      gsap.to(chatContainerRef.current, {
        scale: 0.8,
        opacity: 0,
        y: 20,
        duration: 0.3,
        ease: 'power2.in',
        onComplete: () => {
          gsap.set(chatContainerRef.current, { visibility: 'hidden' });
        }
      });
    }
  }, [isOpen]);

  // Check if scroll is near bottom
  const isNearBottom = useCallback(() => {
    if (!messagesContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    // Consider "near bottom" if within 100px of the bottom
    return scrollHeight - scrollTop - clientHeight < 100;
  }, []);

  // Helper function to scroll to a specific message by ID
  const scrollToSpecificMessage = useCallback((messageId: string, force: boolean = false) => {
    if (!messagesContainerRef.current) return;

    // If user manually scrolled away and we're not forcing the scroll
    if (userHasScrolled.current && !force && !isNearBottom()) {
      return;
    }

    // Kill any existing scroll animation
    if (scrollTween.current) {
      scrollTween.current.kill();
    }

    // Find the message element by ID
    const messageElement = document.getElementById(messageId);

    if (messageElement) {
      // Find the actual message bubble inside the message container
      const messageBubble = messageElement.querySelector('.message-bubble');

      // Default target is the message bubble or message container
      let targetElement = messageBubble || messageElement;

      if (messageBubble) {
        // First try: Find first paragraph in prose container
        const proseContent = messageBubble.querySelector('.prose');
        if (proseContent) {
          const firstContentElement = proseContent.querySelector('p, h1, h2, h3, h4, h5, h6, ul, ol, pre');
          if (firstContentElement) {
            targetElement = firstContentElement;
          }
        } else {
          // Second try: Find first child with actual content
          const children = Array.from(messageBubble.children);
          for (const child of children) {
            // Skip timestamp and copy button elements
            if (!child.classList.contains('text-xs') && !child.classList.contains('absolute')) {
              targetElement = child;
              break;
            }
          }
        }
      }

      // Create a new smooth scroll animation targeting the beginning of the message
      scrollTween.current = gsap.to(messagesContainerRef.current, {
        duration: 0.7,
        scrollTo: {
          y: targetElement,
          offsetY: 10,
          autoKill: true
        },
        ease: 'power3.out',
        onComplete: () => {
          if (isNearBottom()) {
            userHasScrolled.current = false;
          }
        }
      });
      return;
    }

    // Fallback to scrolling to bottom if message element not found
    scrollTween.current = gsap.to(messagesContainerRef.current, {
      duration: 0.7,
      scrollTo: { y: 'max', autoKill: true },
      ease: 'power3.out',
      onComplete: () => {
        if (isNearBottom()) {
          userHasScrolled.current = false;
        }
      }
    });
  }, [isNearBottom]);

  // Scroll to the beginning of the latest message with buttery smooth animation
  const scrollToLatestMessage = useCallback((force: boolean = false) => {
    if (!messagesContainerRef.current) return;

    // If user manually scrolled away and we're not forcing the scroll
    if (userHasScrolled.current && !force && !isNearBottom()) {
      return;
    }

    // Kill any existing scroll animation
    if (scrollTween.current) {
      scrollTween.current.kill();
    }

    // Find the latest message element by ID
    if (messages.length > 0) {
      const latestMessageId = `message-${messages[messages.length - 1].id}`;
      const latestMessageElement = document.getElementById(latestMessageId);

      if (latestMessageElement) {
        // Find the actual message bubble inside the message container
        const messageBubble = latestMessageElement.querySelector('.message-bubble');

        // Default target is the message bubble or message container
        let targetElement = messageBubble || latestMessageElement;

        if (messageBubble) {
          // First try: Find first paragraph in prose container
          const proseContent = messageBubble.querySelector('.prose');
          if (proseContent) {
            const firstContentElement = proseContent.querySelector('p, h1, h2, h3, h4, h5, h6, ul, ol, pre');
            if (firstContentElement) {
              targetElement = firstContentElement;
            }
          } else {
            // Second try: Find first child with actual content
            const children = Array.from(messageBubble.children);
            for (const child of children) {
              // Skip timestamp and copy button elements
              if (!child.classList.contains('text-xs') && !child.classList.contains('absolute')) {
                targetElement = child;
                break;
              }
            }
          }
        }

        // Console log for debugging
        console.log('Scrolling to:', targetElement);

        // Create a new smooth scroll animation targeting the beginning of the latest message
        scrollTween.current = gsap.to(messagesContainerRef.current, {
          duration: 0.7,
          scrollTo: {
            y: targetElement,
            offsetY: 10, // Reduced offset to get closer to the top of the content
            autoKill: true
          },
          ease: 'power3.out',
          onComplete: () => {
            if (isNearBottom()) {
              userHasScrolled.current = false;
            }
          }
        });
        return;
      }
    }

    // Fallback to scrolling to bottom if message element not found
    scrollTween.current = gsap.to(messagesContainerRef.current, {
      duration: 0.7,
      scrollTo: { y: 'max', autoKill: true },
      ease: 'power3.out',
      onComplete: () => {
        if (isNearBottom()) {
          userHasScrolled.current = false;
        }
      }
    });
  }, [isNearBottom, messages]);

  // Handle manual scrolling detection
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      // If we were already at the bottom and are still at the bottom, this isn't a manual scroll
      const wasAtBottom = isNearBottom();
      const { scrollTop } = container;

      // Only consider it a manual scroll if we're scrolling upward
      if (scrollTop < lastScrollPosition.current && !wasAtBottom) {
        userHasScrolled.current = true;
      }
      // If user scrolled to bottom, reset the flag
      else if (wasAtBottom) {
        userHasScrolled.current = false;
      }

      lastScrollPosition.current = scrollTop;
    };

    container.addEventListener('scroll', handleScroll, { passive: true });
    return () => container.removeEventListener('scroll', handleScroll);
  }, [isNearBottom]);

  // Initial scroll only
  useEffect(() => {
    if (messages.length === 0) {
      // Initial load only
      scrollToLatestMessage(true);
    }
  }, [messages.length, scrollToLatestMessage]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Toggle chat with Cmd+K or Ctrl+K
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsOpen(prev => !prev);
      }

      // Submit form with Cmd+Enter or Ctrl+Enter
      if (e.key === 'Enter' && (e.metaKey || e.ctrlKey) && formRef.current) {
        e.preventDefault();
        formRef.current.requestSubmit();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  // Handle outside clicks to close chat
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (e: MouseEvent) => {
      if (
        chatContainerRef.current &&
        !chatContainerRef.current.contains(e.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(e.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);



  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    const messageText = inputValue.trim();
    if (!messageText || isTyping) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      text: messageText,
      sender: 'user',
      timestamp: new Date(),
    };

    // Prepare tool definitions from MCP tools
    const toolDefinitions = tools?.map(tool => ({
      name: tool.name,
      description: tool.description || '',
      parameters: tool.inputSchema,
    })) || [];

    const aiMessageId = `ai-${Date.now()}`;
    const aiMessagePlaceholder: Message = {
      id: aiMessageId,
      text: '',
      sender: 'ai',
      timestamp: new Date(),
      isTyping: true, // Use isTyping to show a placeholder/loader
    };

    setMessages((prev) => [...prev, userMessage, aiMessagePlaceholder]);
    setInputValue('');
    setIsTyping(true);

    // Force scroll to the user message immediately
    setTimeout(() => {
      const userMessageId = `message-${userMessage.id}`;
      scrollToSpecificMessage(userMessageId, true);
    }, 50);

    let accumulatedResponse = '';

    const onChunk = (chunk: string) => {
      accumulatedResponse += chunk;
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === aiMessageId
            ? { ...msg, text: accumulatedResponse, isTyping: false } // Update text and stop typing animation
            : msg
        )
      );
    };

    const onError = (error: string) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === aiMessageId
            ? { ...msg, text: `Error: ${error}`, isError: true, isTyping: false }
            : msg
        )
      );
    };

    const onComplete = () => {
      // Before removing typing indicator, find the current message element
      const messageElement = document.getElementById(`message-${aiMessageId}`);
      setIsTyping(false);

      // When the message is complete, wait for the DOM to update with the final content
      setTimeout(() => {
        // Find the message bubble that replaced the typing animation
        const messageBubble = messageElement?.querySelector('.message-bubble');

        if (messageBubble) {
          // Create a buttery smooth transition to the new content
          gsap.to(messagesContainerRef.current, {
            duration: 0.9,
            scrollTo: {
              y: messageBubble,
              offsetY: 10,
              autoKill: true
            },
            ease: 'power2.inOut',
            overwrite: true
          });
          console.log('Transition: Scrolling from typing dots to message content');
        } else {
          // Fallback to standard method
          scrollToSpecificMessage(`message-${aiMessageId}`, true);
        }
      }, 100);
    };

    await searchCloudflareAI(messageText, aiMessageId, onChunk, onError, onComplete, toolDefinitions);

  };

  // Function to fetch response from the API
  const searchCloudflareAI = useCallback(async (
    query: string,
    aiMsgId: string,
    onChunk: (text: string) => void,
    onError: (error: string) => void,
    onComplete: () => void,
    toolDefinitions: Array<{ name: string; description: string; parameters: any }> = []
  ) => {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          messages: messages.map(msg => ({
            role: msg.sender === 'user' ? 'user' : 'assistant',
            content: msg.text,
          })),
          tools: toolDefinitions,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        onError(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
        onComplete();
        return;
      }

      // Check if response is streaming or complete
      const contentType = response.headers.get('content-type');
      
      if (contentType?.includes('application/json')) {
        // Handle complete JSON response
        const data = await response.json();
        console.log('API Response:', data);

        if (data.content) {
          // Check if this was a tool call response
          if (data.isToolCall) {
            console.log('Tool call executed successfully:', data.toolResults);
            onChunk(`🔧 ${data.content}`);
          } else {
            onChunk(data.content);
          }
        } else if (data.response) {
          onChunk(data.response);
        } else {
          onChunk('Received an empty response');
        }
        onComplete();
        return;
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        onError('Failed to get response reader.');
        onComplete();
        return;
      }

      const decoder = new TextDecoder();
      let done = false;
      let buffer = '';

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        const text = decoder.decode(value, { stream: true });

        buffer += text;

        let lastProcessedIndex = 0;
        const pattern = /data: ({[^}]*})/g;
        let match;

        while ((match = pattern.exec(buffer)) !== null) {
          try {
            const jsonData = JSON.parse(match[1]);

            if (jsonData.response !== undefined) {
              onChunk(jsonData.response);

              if (messagesContainerRef.current) {
                if (jsonData.response && jsonData.response.length < 100) {
                  const messageContainer = document.getElementById(`message-${aiMsgId}`);
                  if (messageContainer) {
                    const messageBubble = messageContainer.querySelector('.message-bubble');
                    if (messageBubble) {
                      gsap.to(messagesContainerRef.current, {
                        duration: 0.9,
                        scrollTo: {
                          y: messageBubble,
                          offsetY: 10,
                          autoKill: true
                        },
                        ease: 'power2.inOut',
                        overwrite: true
                      });
                    } else {
                      scrollToSpecificMessage(`message-${aiMsgId}`, true);
                    }
                  } else {
                    scrollToSpecificMessage(`message-${aiMsgId}`, true);
                  }
                } else if (isNearBottom()) {
                  messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
                }
              }
            }

            lastProcessedIndex = match.index + match[0].length;
          } catch {
            console.warn('Malformed JSON in chunk:', match[1]);
          }
        }

        if (lastProcessedIndex > 0) {
          buffer = buffer.substring(lastProcessedIndex);
        }

        if (buffer.includes('data: [DONE]')) {
          done = true;
          break;
        }
      }

      onComplete();
    } catch (e) {
      console.error('Error in send message:', e);
      onError('An error occurred while processing your request.');
      onComplete();
    }
  }, [isNearBottom, scrollToSpecificMessage]);

  const toggleChat = () => {
    setIsOpen(prev => {
      if (timeline.current) {
        if (!prev) {
          timeline.current.play();
        } else {
          timeline.current.reverse();
        }
      }
      return !prev;
    });
  };

  return (
    <>
      {/* Chat Container */}
      <div
        ref={chatContainerRef}
        className="fixed bottom-24 right-6 z-50 w-96 h-[70vh] max-h-[700px] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl flex flex-col overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-900 to-blue-700 text-white p-4 flex justify-between items-center flex-shrink-0">
          <div className="flex items-center space-x-2">
            <Sparks className="w-5 h-5 text-white transform scale-x-[-1]" />
            <h2 className="font-semibold">Nacit AI</h2>
          </div>
          <button
            onClick={toggleChat}
            className="text-white hover:text-gray-200 focus:outline-none"
            aria-label="Close chat"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>

        {/* Messages */}
        <div ref={messagesContainerRef} className="flex-1 p-4 overflow-y-auto bg-gray-50 dark:bg-gray-800 transition-colors duration-200">
          <div className="space-y-4">
            {messages.length === 0 ? (
              <div className="h-full flex flex-col items-center justify-center text-center p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">How can I help you today?</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Ask me anything about NACIT.</p>
              </div>
            ) : (
              messages.map((message) => (
                <MessageItem key={message.id} message={message} onUpdate={scrollToLatestMessage} id={`message-${message.id}`} />
              ))
            )}
          </div>
        </div>

        {/* Input */}
        <form
          ref={formRef}
          onSubmit={handleSendMessage}
          className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4 flex-shrink-0"
        >
          <div className="relative">
            <div className={`relative flex flex-row justify-center border ${isTyping ? 'border-transparent ring-2 ring-offset-0 ring-blue-500/70 shadow-lg shadow-blue-500/30 animate-pulse-slow' : 'border-gray-300 dark:border-gray-600'} rounded-xl pr-1 transition-all duration-300 ${isTyping ? 'bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 bg-size-200 bg-pos-0 animate-gradient' : ''}`}>
              <textarea
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.metaKey) {
                    e.preventDefault();
                    handleSendMessage(e);
                  }
                }}
                placeholder={isTyping ? 'Thinking...' : 'Ask me anything...'}
                className="w-full min-h-[44px] max-h-32 px-4 py-2.5 pr-12 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none overflow-y-auto"
                rows={1}
                disabled={isTyping}
                aria-label={isTyping ? 'Thinking...' : 'Ask me anything'}
                aria-describedby="send-button"
              />
              <div className="flex items-center space-x-1">
                <button
                  type="submit"
                  id="send-button"
                  disabled={!inputValue.trim() || isTyping || inputValue.length > 2000}
                  className={`p-1.5 rounded-full ${inputValue.trim() && inputValue.length <= 2000 ? 'text-blue-600 hover:bg-blue-100 dark:text-blue-400 dark:hover:bg-gray-700' : 'text-gray-400 dark:text-gray-600 cursor-not-allowed'} transition-colors`}
                  aria-label="Send message"
                >
                  {isTyping ? (
                    <SystemRestart className="w-5 h-5 animate-spin" />
                  ) : (
                    <SendDiagonal className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>
            <div className='flex justify-end'>
              <span className={`text-xs px-1.5 py-0.5 rounded ${inputValue.length > 2000 ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' : 'text-gray-400 dark:text-gray-500'}`}>
                {inputValue.length}/2000
              </span>
            </div>
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 flex justify-center px-1">
              <span>Press Enter to send, Shift+Enter for new line</span>
            </div>
          </div>
        </form>
      </div>

      {/* Floating Action Button */}
      <button
        ref={buttonRef}
        onClick={toggleChat}
        style={{ position: 'fixed', bottom: '1.5rem', right: '1.5rem' }}
        className="w-12 h-12 bg-blue-600 rounded-full shadow-xl flex items-center justify-center text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label={isOpen ? 'Close chat' : 'Open AI Assistant'}
        aria-expanded={isOpen}
      >
        <div ref={chatIconRef} className="absolute">
          <ChatBubble className="h-6 w-6 text-white" />
        </div>
        <div ref={closeIconRef} className="absolute">
          <div className="h-6 w-6 text-white" style={{ transform: 'rotate(45deg)' }}>
            <span className="absolute top-1/2 left-0 w-full h-0.5 bg-white"></span>
            <span className="absolute top-1/2 left-0 w-full h-0.5 bg-white" style={{ transform: 'rotate(90deg)' }}></span>
          </div>
        </div>
        {messages.filter(m => m.sender === 'ai').length > 0 && !isOpen && (
          <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
            {messages.filter(m => m.sender === 'ai').length}
          </span>
        )}
      </button>
    </>
  );
}
