'use client';

import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { MarkdownRenderer } from './MarkdownRenderer';

interface AnimatedContentProps {
  text: string;
  onUpdate: () => void;
}

export function AnimatedContent({ text, onUpdate }: AnimatedContentProps) {
  // Store previous text length to detect incremental updates
  const prevTextRef = useRef<string>('');
  const contentRef = useRef<HTMLDivElement>(null);
  // Track which text has already been animated
  const animatedTextRef = useRef<Set<string>>(new Set());
  // Track if component has been fully mounted
  const isMountedRef = useRef<boolean>(false);

  useEffect(() => {
    if (!contentRef.current) return;
    
    // Check if this is an incremental update
    const isIncremental = text.startsWith(prevTextRef.current) && text.length > prevTextRef.current.length;
    
    // Get only the new text (for incremental updates)
    // const newTextPortion = isIncremental ? text.substring(prevTextRef.current.length) : text;
    
    // Don't animate if we've already animated this text or if it's empty
    if (text.trim() === '' || animatedTextRef.current.has(text)) {
      prevTextRef.current = text;
      return;
    }
    
    // Update the previous text reference
    prevTextRef.current = text;
    
    // Mark this text as animated
    animatedTextRef.current.add(text);
    
    // On first render of existing content, don't animate (prevents animation on page load)
    if (!isMountedRef.current) {
      isMountedRef.current = true;
      if (!isIncremental) return; // Skip animation for initial content
    }
    
    // Small delay to ensure React has finished rendering
    // This is the key to avoiding the NotFoundError
    const timeoutId = setTimeout(() => {
      if (!contentRef.current) return;
      const container = contentRef.current;
      const newWords: HTMLSpanElement[] = [];

      // Use a TreeWalker to find all text nodes
      // But be more selective about which nodes to process
      const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: function(node) {
            // Skip nodes without parent elements
            if (!node.parentElement) return NodeFilter.FILTER_REJECT;
            
            // Check if the node or any of its parents is in a code block or pre
            let el = node.parentElement;
            while (el && el !== container) {
              // Skip text nodes inside code blocks (div.not-prose containing pre)
              if (el.classList.contains('not-prose') && el.querySelector('pre')) {
                return NodeFilter.FILTER_REJECT;
              }
              // Skip inline code elements
              if (el.tagName === 'CODE') {
                return NodeFilter.FILTER_REJECT;
              }
              // Skip headings - they often cause rendering issues
              if (['H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(el.tagName)) {
                return NodeFilter.FILTER_REJECT;
              }
              // Skip any elements that should not be animated
              if (el.getAttribute('data-no-animate') === 'true') {
                return NodeFilter.FILTER_REJECT;
              }
              el = el.parentElement;
            }
            
            // Skip text nodes that contain only whitespace
            if (node.textContent?.trim() === '') {
              return NodeFilter.FILTER_REJECT;
            }
            
            // Accept only if node's parent is a standard text-containing element
            // like paragraphs, list items, spans, divs
            const validParentTags = ['P', 'LI', 'SPAN', 'DIV', 'TD', 'A'];
            return validParentTags.includes(node.parentElement.tagName)
              ? NodeFilter.FILTER_ACCEPT
              : NodeFilter.FILTER_REJECT;
          },
        }
      );
      
      const textNodes: Node[] = [];
      let currentNode = walker.nextNode();
      
      while (currentNode) {
        // Ensure we don't process text within already animated spans
        if (!currentNode.parentElement?.classList.contains('word')) {
          textNodes.push(currentNode);
        }
        currentNode = walker.nextNode();
      }

      // Split text nodes into words and wrap them in spans
      textNodes.forEach((node) => {
        // Skip if node is no longer in DOM (prevents NotFoundError)
        if (!node.isConnected || !node.parentNode) return;
        
        const words = node.textContent?.split(/(\s+)/) || [];
        if (words.length === 0) return;

        const fragment = document.createDocumentFragment();
        words.forEach((word) => {
          if (word.trim() !== '') {
            const span = document.createElement('span');
            span.textContent = word;
            span.className = 'word inline-block';
            fragment.appendChild(span);
            newWords.push(span);
          } else {
            fragment.appendChild(document.createTextNode(word));
          }
        });

        // Safety check before DOM modification
        if (node.parentNode && node.isConnected) {
          node.parentNode.replaceChild(fragment, node);
        }
      });

      // Animate the newly created word spans
      if (newWords.length > 0) {
        gsap.fromTo(
          newWords,
          { opacity: 0 },
          {
            opacity: 1,
            duration: 0.3,
            stagger: 0.02,
            ease: 'power2.out',
            onComplete: onUpdate
          }
        );
      } else {
        // Call onUpdate even if no animations were performed
        onUpdate();
      }
    }, 50); // Small delay to ensure React has finished rendering

    return () => clearTimeout(timeoutId);
  }, [text, onUpdate]);

  return (
    <div 
      ref={contentRef} 
      // Don't use key={text} for streaming updates, as it causes full remounts
      // which breaks the streaming experience
      className="prose prose-sm dark:prose-invert max-w-none"
    >
      <MarkdownRenderer content={text} />
    </div>
  );
}
