'use client';

import React from 'react';
import { CSSProperties } from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import Image from 'next/image';

// Create a properly typed style object for code blocks
const codeBlockStyle: { [key: string]: CSSProperties } = {
  ...vscDarkPlus,
  'pre[class*="language-"]': {
    ...vscDarkPlus['pre[class*="language-"]'],
    margin: 0,
    borderRadius: '0.5rem',
    backgroundColor: '#1f2937',
    fontSize: '0.875rem',
    lineHeight: 1.5,
  },
  'code[class*="language-"]': {
    ...vscDarkPlus['code[class*="language-"]'],
    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
  },
};

interface MarkdownRendererProps {
  content: string;
  className?: string;
  proseClassName?: string;
}

export function MarkdownRenderer({ 
  content, 
  className = '',
  proseClassName = 'prose-xs sm:prose-sm dark:prose-invert max-w-none'
}: MarkdownRendererProps) {
  return (
    <div className={`${proseClassName} ${className}`}>
      <ReactMarkdown
        components={{
          // Code blocks with syntax highlighting
          code({ className: codeClassName, children }) {
            const match = /language-(\w+)/.exec(codeClassName || '');
            const codeContent = String(children).replace(/\n$/, '');
            
            if (match) {
              return (
                <div className="not-prose my-4">
                  <SyntaxHighlighter
                    style={codeBlockStyle}
                    language={match[1]}
                    PreTag="div"
                    customStyle={{
                      margin: 0,
                      borderRadius: '0.5rem',
                      backgroundColor: '#1f2937',
                      fontSize: '0.875rem',
                      lineHeight: 1.5,
                    }}
                    codeTagProps={{
                      style: {
                        fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                      },
                    }}
                  >
                    {codeContent}
                  </SyntaxHighlighter>
                </div>
              );
            }
            
            return (
              <code className="bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-sm font-mono text-gray-800 dark:text-gray-200">
                {children}
              </code>
            );
          },
          
          // Links
          a: ({ ...props }) => (
            <a 
              {...props} 
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200"
            />
          ),
          
          // Headings
          h1: ({ ...props }) => (
            <h1 className="text-3xl font-bold mt-8 mb-4 text-gray-900 dark:text-white" {...props} />
          ),
          h2: ({ ...props }) => (
            <h2 className="text-2xl font-semibold mt-6 mb-3 text-gray-800 dark:text-gray-100" {...props} />
          ),
          h3: ({ ...props }) => (
            <h3 className="text-xl font-semibold mt-5 mb-2.5 text-gray-800 dark:text-gray-100" {...props} />
          ),
          h4: ({ ...props }) => (
            <h4 className="text-lg font-medium mt-4 mb-2 text-gray-800 dark:text-gray-100" {...props} />
          ),
          
          // Lists
          ul: ({ ...props }) => (
            <ul className="list-disc pl-6 space-y-1.5 my-4" {...props} />
          ),
          ol: ({ ...props }) => (
            <ol className="list-decimal pl-6 space-y-1.5 my-4" {...props} />
          ),
          li: ({ children, ...props }) => (
            <li className="my-1.5 pl-1" {...props}>{children}</li>
          ),
          
          // Bold-italic text via <em><strong>some_text</strong></em>  
          em: ({ children, ...props }) => {
            // We removed code that was using the node property since it was causing ESLint errors
            return <em {...props}>{children}</em>;
          },
          
          // Blockquotes
          blockquote: ({ ...props }) => (
            <blockquote 
              className="border-l-4 border-blue-500 dark:border-blue-400 pl-4 my-6 italic text-gray-700 dark:text-gray-300"
              {...props} 
            />
          ),
          
          // Images
          img: ({ ...props }) => (
            <div className="my-6 rounded-lg overflow-hidden shadow-md">
              <div className="relative w-full h-auto" style={{ minHeight: '200px' }}>
                <Image
                  src={typeof props.src === 'string' ? props.src : ''}
                  alt={props.alt || ''}
                  fill
                  style={{ objectFit: 'contain' }}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
              {props.alt && (
                <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-2">
                  {props.alt}
                </p>
              )}
            </div>
          ),
          
          // Tables
          table: ({ ...props }) => (
            <div className="overflow-x-auto my-6">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700" {...props} />
            </div>
          ),
          thead: ({ ...props }) => (
            <thead className="bg-gray-50 dark:bg-gray-800" {...props} />
          ),
          tbody: ({ ...props }) => (
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700" {...props} />
          ),
          tr: ({ ...props }) => (
            <tr className="hover:bg-gray-50 dark:hover:bg-gray-800/50" {...props} />
          ),
          th: ({ ...props }) => (
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              {...props} 
            />
          ),
          td: ({ ...props }) => (
            <td 
              className="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200"
              {...props} 
            />
          ),
          
          // Horizontal rule
          hr: ({ ...props }) => (
            <hr className="my-8 border-gray-200 dark:border-gray-700" {...props} />
          ),
          
          // Paragraphs
          p: ({ ...props }) => (
            <p className="my-4 leading-relaxed text-gray-700 dark:text-gray-300" {...props} />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
