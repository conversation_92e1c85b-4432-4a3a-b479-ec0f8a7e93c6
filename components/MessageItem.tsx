'use client';

import { useRef, useState } from 'react';
import { useGSAP } from '@gsap/react';
import { gsap } from 'gsap';
import { FiCopy, FiCheck } from 'react-icons/fi';
import { MarkdownRenderer } from './MarkdownRenderer';
import { AnimatedContent } from './AnimatedContent';

type Message = {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isError?: boolean;
  isTyping?: boolean;
};

interface MessageItemProps {
  message: Message;
  onUpdate: () => void;
  id?: string;
}

export function MessageItem({ message, onUpdate, id }: MessageItemProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(message.text).then(() => {
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    });
  };

  // Only animate once when component is first mounted
  const hasAnimated = useRef(false);
  
  useGSAP(() => {
    // Only run animation if this is the first time
    if (!hasAnimated.current && containerRef.current) {
      gsap.from(containerRef.current, {
        opacity: 0,
        y: 15,
        duration: 0.4,
        ease: 'power2.out',
      });
      // Mark as animated so we don't animate again
      hasAnimated.current = true;
    }
  }, { scope: containerRef, dependencies: [] });

  return (
    <div
      ref={containerRef}
      id={id}
      className={`flex ${
        message.sender === 'user' ? 'justify-end' : 'justify-start'
      }`}
    >
      {message.isTyping ? (
        <div className="flex items-center space-x-1 bg-white dark:bg-gray-700 p-3 rounded-lg shadow">
          <div className="w-2 h-2 rounded-full bg-blue-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 rounded-full bg-blue-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 rounded-full bg-blue-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
      ) : (
        <div
          className={`message-bubble relative group max-w-[90%] p-4 rounded-2xl break-words ${
            message.sender === 'user'
              ? 'bg-blue-600 text-white ml-auto'
              : 'bg-white dark:bg-gray-700 border dark:border-gray-600'
          } ${
            message.isError ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100' : ''
          }`}
        >
          {
            message.sender === 'ai' && !message.isError && message.text
              ? <AnimatedContent text={message.text} onUpdate={onUpdate} />
              : <div className={`prose prose-sm ${message.sender === 'user' ? '!text-white prose-headings:!text-white prose-p:!text-white prose-strong:!text-white prose-em:!text-white prose-code:!text-white' : 'dark:prose-invert'} max-w-none`}>
                  <MarkdownRenderer content={message.text} />
                </div>
          }
          <div className="flex justify-between items-center mt-2">
            <p 
              className={`text-xs opacity-70 ${
                message.sender === 'user' ? 'text-white' : 'text-gray-500 dark:text-gray-400'
              }`}
            >
              {message.timestamp.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </p>
            {message.sender === 'ai' && !message.isError && !message.isTyping && (
              <button
                onClick={handleCopy}
                className="opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-md bg-white/10 hover:bg-white/20"
              >
                {isCopied ? (
                  <FiCheck className="w-4 h-4 text-green-400" />
                ) : (
                  <FiCopy className="w-4 h-4 text-gray-400" />
                )}
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
